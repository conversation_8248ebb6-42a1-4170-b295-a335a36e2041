<?php

namespace App\Controllers;

class TestController extends BaseController
{
    public function index()
    {
        return 'Test Controller is working! CodeIgniter 4 is running properly.';
    }

    public function loginTest()
    {
        // Test if we can render a simple view
        $data = [
            'title' => 'Login Test',
            'message' => 'This is a test to see if views are working.'
        ];

        try {
            return view('dakoii/dakoii_login', $data);
        } catch (\Exception $e) {
            return 'Error rendering view: ' . $e->getMessage();
        }
    }

    public function dbTest()
    {
        try {
            $db = \Config\Database::connect();

            // Test basic connection
            $query = $db->query('SELECT 1 as test');
            $result = $query->getRow();

            $output = "Database connection: OK<br>";
            $output .= "Test query result: " . $result->test . "<br><br>";

            // Test if dakoii_users table exists
            $tables = $db->listTables();
            $output .= "Available tables:<br>";
            foreach ($tables as $table) {
                $output .= "- " . $table . "<br>";
            }

            // Test if we can query dakoii_users
            if (in_array('dakoii_users', $tables)) {
                $query = $db->query('SELECT COUNT(*) as count FROM dakoii_users');
                $result = $query->getRow();
                $output .= "<br>dakoii_users table: " . $result->count . " records<br>";
            }

            return $output;

        } catch (\Exception $e) {
            return 'Database error: ' . $e->getMessage();
        }
    }
}
