<?php

namespace App\Controllers;

use App\Models\DakoiiUser;
use App\Models\OrganizationModel;

class DakoiiDashboardController extends BaseController
{
    protected $dakoiiUserModel;
    protected $organizationModel;
    protected $session;

    public function __construct()
    {
        $this->dakoiiUserModel = new DakoiiUser();
        $this->organizationModel = new OrganizationModel();
        $this->session = \Config\Services::session();
    }

    /**
     * Dashboard main page
     * Task 2.1: getDashboardStats
     */
    public function index()
    {
        $userId = $this->session->get('dakoii_user_id');
        $user = $this->dakoiiUserModel->find($userId);

        if (!$user) {
            return redirect()->to('/dakoii/logout');
        }

        // Get dashboard statistics
        $stats = $this->getDashboardStats();
        
        // Get recent activity (last 10 activities)
        $recentActivity = $this->getRecentActivity();

        $data = [
            'title' => 'Dashboard - Dakoii Portal',
            'page_title' => 'Dashboard',
            'user_name' => $user['name'],
            'stats' => $stats,
            'recent_activity' => $recentActivity,
            'user' => $user
        ];

        return view('dakoii/dakoii_dashboard', $data);
    }

    /**
     * Get dashboard statistics
     * Task 2.1: Database query optimization & Dashboard data preparation
     */
    private function getDashboardStats(): array
    {
        // Organizations statistics
        $totalOrganizations = $this->organizationModel->countAllResults();
        $activeOrganizations = $this->organizationModel->where('is_active', 1)->countAllResults();
        $paidLicenses = $this->organizationModel->where('license_status', 'paid')->countAllResults();
        $unpaidLicenses = $this->organizationModel->where('license_status', 'unpaid')->countAllResults();

        // Dakoii users statistics
        $totalDakoiiUsers = $this->dakoiiUserModel->countAllResults();
        $activatedUsers = $this->dakoiiUserModel->where('is_activated', 1)->countAllResults();
        $pendingUsers = $this->dakoiiUserModel->where('is_activated', 0)->countAllResults();

        // Calculate percentages and trends
        $organizationGrowth = $this->calculateGrowthPercentage('organizations');
        $userGrowth = $this->calculateGrowthPercentage('dakoii_users');

        return [
            'organizations' => [
                'total' => $totalOrganizations,
                'active' => $activeOrganizations,
                'inactive' => $totalOrganizations - $activeOrganizations,
                'paid_licenses' => $paidLicenses,
                'unpaid_licenses' => $unpaidLicenses,
                'growth_percentage' => $organizationGrowth
            ],
            'dakoii_users' => [
                'total' => $totalDakoiiUsers,
                'activated' => $activatedUsers,
                'pending' => $pendingUsers,
                'growth_percentage' => $userGrowth
            ],
            'license_revenue' => [
                'paid_percentage' => $totalOrganizations > 0 ? round(($paidLicenses / $totalOrganizations) * 100, 1) : 0,
                'unpaid_percentage' => $totalOrganizations > 0 ? round(($unpaidLicenses / $totalOrganizations) * 100, 1) : 0
            ]
        ];
    }

    /**
     * Calculate growth percentage compared to last month
     */
    private function calculateGrowthPercentage(string $table): float
    {
        $db = \Config\Database::connect();
        
        // Current month count
        $currentMonth = $db->table($table)
            ->where('created_at >=', date('Y-m-01'))
            ->where('deleted_at', null)
            ->countAllResults();

        // Last month count
        $lastMonthStart = date('Y-m-01', strtotime('-1 month'));
        $lastMonthEnd = date('Y-m-t', strtotime('-1 month'));
        
        $lastMonth = $db->table($table)
            ->where('created_at >=', $lastMonthStart)
            ->where('created_at <=', $lastMonthEnd . ' 23:59:59')
            ->where('deleted_at', null)
            ->countAllResults();

        if ($lastMonth == 0) {
            return $currentMonth > 0 ? 100 : 0;
        }

        return round((($currentMonth - $lastMonth) / $lastMonth) * 100, 1);
    }

    /**
     * Get recent activity for dashboard
     */
    private function getRecentActivity(): array
    {
        $activities = [];

        // Recent organizations (last 5)
        $recentOrgs = $this->organizationModel
            ->select('name, created_at, created_by')
            ->orderBy('created_at', 'DESC')
            ->limit(5)
            ->findAll();

        foreach ($recentOrgs as $org) {
            $activities[] = [
                'type' => 'organization_created',
                'message' => 'New organization "' . $org['name'] . '" was created',
                'timestamp' => $org['created_at'],
                'icon' => 'building'
            ];
        }

        // Recent users (last 5)
        $recentUsers = $this->dakoiiUserModel
            ->select('name, created_at, created_by')
            ->orderBy('created_at', 'DESC')
            ->limit(5)
            ->findAll();

        foreach ($recentUsers as $user) {
            $activities[] = [
                'type' => 'user_created',
                'message' => 'New user "' . $user['name'] . '" was created',
                'timestamp' => $user['created_at'],
                'icon' => 'user'
            ];
        }

        // Sort activities by timestamp
        usort($activities, function($a, $b) {
            return strtotime($b['timestamp']) - strtotime($a['timestamp']);
        });

        return array_slice($activities, 0, 10);
    }

    /**
     * Get chart data for dashboard
     */
    public function getChartData()
    {
        $type = $this->request->getGet('type');
        
        switch ($type) {
            case 'organizations_monthly':
                return $this->response->setJSON($this->getOrganizationsMonthlyData());
            
            case 'license_status':
                return $this->response->setJSON($this->getLicenseStatusData());
            
            case 'user_registrations':
                return $this->response->setJSON($this->getUserRegistrationsData());
            
            default:
                return $this->response->setStatusCode(400)->setJSON(['error' => 'Invalid chart type']);
        }
    }

    /**
     * Get organizations monthly data for charts
     */
    private function getOrganizationsMonthlyData(): array
    {
        $db = \Config\Database::connect();
        
        $query = $db->query("
            SELECT 
                DATE_FORMAT(created_at, '%Y-%m') as month,
                COUNT(*) as count
            FROM organizations 
            WHERE created_at >= DATE_SUB(NOW(), INTERVAL 12 MONTH)
            AND deleted_at IS NULL
            GROUP BY DATE_FORMAT(created_at, '%Y-%m')
            ORDER BY month ASC
        ");

        $results = $query->getResultArray();
        
        $labels = [];
        $data = [];
        
        foreach ($results as $result) {
            $labels[] = date('M Y', strtotime($result['month'] . '-01'));
            $data[] = (int)$result['count'];
        }

        return [
            'labels' => $labels,
            'datasets' => [
                [
                    'label' => 'Organizations Created',
                    'data' => $data,
                    'borderColor' => '#FF006E',
                    'backgroundColor' => 'rgba(255, 0, 110, 0.1)',
                    'tension' => 0.4
                ]
            ]
        ];
    }

    /**
     * Get license status data for pie chart
     */
    private function getLicenseStatusData(): array
    {
        $paid = $this->organizationModel->where('license_status', 'paid')->countAllResults();
        $unpaid = $this->organizationModel->where('license_status', 'unpaid')->countAllResults();

        return [
            'labels' => ['Paid Licenses', 'Unpaid Licenses'],
            'datasets' => [
                [
                    'data' => [$paid, $unpaid],
                    'backgroundColor' => ['#06FFA5', '#FF006E'],
                    'borderWidth' => 0
                ]
            ]
        ];
    }

    /**
     * Get user registrations data
     */
    private function getUserRegistrationsData(): array
    {
        $db = \Config\Database::connect();
        
        $query = $db->query("
            SELECT 
                DATE_FORMAT(created_at, '%Y-%m') as month,
                COUNT(*) as count
            FROM dakoii_users 
            WHERE created_at >= DATE_SUB(NOW(), INTERVAL 6 MONTH)
            AND deleted_at IS NULL
            GROUP BY DATE_FORMAT(created_at, '%Y-%m')
            ORDER BY month ASC
        ");

        $results = $query->getResultArray();
        
        $labels = [];
        $data = [];
        
        foreach ($results as $result) {
            $labels[] = date('M Y', strtotime($result['month'] . '-01'));
            $data[] = (int)$result['count'];
        }

        return [
            'labels' => $labels,
            'datasets' => [
                [
                    'label' => 'User Registrations',
                    'data' => $data,
                    'backgroundColor' => '#8338EC',
                    'borderRadius' => 4
                ]
            ]
        ];
    }

    /**
     * Export dashboard data
     */
    public function exportData()
    {
        $format = $this->request->getGet('format', FILTER_SANITIZE_STRING) ?? 'csv';
        $stats = $this->getDashboardStats();
        
        if ($format === 'json') {
            return $this->response
                ->setHeader('Content-Type', 'application/json')
                ->setHeader('Content-Disposition', 'attachment; filename="dashboard_stats_' . date('Y-m-d') . '.json"')
                ->setJSON($stats);
        }
        
        // Default to CSV
        $csv = "Metric,Value\n";
        $csv .= "Total Organizations," . $stats['organizations']['total'] . "\n";
        $csv .= "Active Organizations," . $stats['organizations']['active'] . "\n";
        $csv .= "Paid Licenses," . $stats['organizations']['paid_licenses'] . "\n";
        $csv .= "Unpaid Licenses," . $stats['organizations']['unpaid_licenses'] . "\n";
        $csv .= "Total Dakoii Users," . $stats['dakoii_users']['total'] . "\n";
        $csv .= "Activated Users," . $stats['dakoii_users']['activated'] . "\n";
        $csv .= "Pending Users," . $stats['dakoii_users']['pending'] . "\n";
        
        return $this->response
            ->setHeader('Content-Type', 'text/csv')
            ->setHeader('Content-Disposition', 'attachment; filename="dashboard_stats_' . date('Y-m-d') . '.csv"')
            ->setBody($csv);
    }
}
