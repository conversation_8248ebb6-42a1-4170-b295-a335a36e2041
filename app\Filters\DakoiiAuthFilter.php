<?php

namespace App\Filters;

use CodeIgniter\Filters\FilterInterface;
use CodeIgniter\HTTP\RequestInterface;
use CodeIgniter\HTTP\ResponseInterface;

class DakoiiAuthFilter implements FilterInterface
{
    /**
     * Do whatever processing this filter needs to do.
     * By default it should not return anything during
     * normal execution. However, when an abnormal state
     * is found, it should return an instance of
     * CodeIgniter\HTTP\Response. If it does, script
     * execution will end and that Response will be
     * sent back to the client, allowing for error pages,
     * redirects, etc.
     *
     * @param RequestInterface $request
     * @param array|null       $arguments
     *
     * @return mixed
     */
    public function before(RequestInterface $request, $arguments = null)
    {
        $session = \Config\Services::session();
        
        // Check if user is logged in
        if (!$session->get('dakoii_logged_in') || !$session->get('dakoii_user_id')) {
            // Store intended URL for redirect after login
            $session->set('intended_url', current_url());
            
            // Redirect to login page
            return redirect()->to('/dakoii')->with('error', 'Please log in to access this page.');
        }

        // Check if session is still valid (not expired)
        $loginTime = $session->get('dakoii_login_time');
        $sessionTimeout = 8 * 60 * 60; // 8 hours in seconds
        
        if ($loginTime && (time() - $loginTime) > $sessionTimeout) {
            // Session expired
            $session->destroy();
            return redirect()->to('/dakoii')->with('error', 'Your session has expired. Please log in again.');
        }

        // Update last activity time
        $session->set('dakoii_last_activity', time());
    }

    /**
     * Allows After filters to inspect and modify the response
     * object as needed. This method does not allow any way
     * to stop execution of other after filters, short of
     * throwing an Exception or Error.
     *
     * @param RequestInterface  $request
     * @param ResponseInterface $response
     * @param array|null        $arguments
     *
     * @return mixed
     */
    public function after(RequestInterface $request, ResponseInterface $response, $arguments = null)
    {
        // Nothing to do here
    }
}
