<?php

namespace App\Controllers;

use App\Models\OrganizationModel;
use App\Models\OrganizationImageModel;
use App\Models\DakoiiUser;

class DakoiiOrganizationController extends BaseController
{
    protected $organizationModel;
    protected $organizationImageModel;
    protected $dakoiiUserModel;
    protected $session;

    public function __construct()
    {
        $this->session = \Config\Services::session();
    }

    private function getOrganizationModel()
    {
        if (!$this->organizationModel) {
            $this->organizationModel = new OrganizationModel();
        }
        return $this->organizationModel;
    }

    private function getOrganizationImageModel()
    {
        if (!$this->organizationImageModel) {
            $this->organizationImageModel = new OrganizationImageModel();
        }
        return $this->organizationImageModel;
    }

    private function getDakoiiUserModel()
    {
        if (!$this->dakoiiUserModel) {
            $this->dakoiiUserModel = new DakoiiUser();
        }
        return $this->dakoiiUserModel;
    }

    /**
     * List organizations with filtering and pagination
     * Task 3.1: listOrganisations
     */
    public function listOrganisations()
    {
        $userId = $this->session->get('dakoii_user_id');
        
        // Get filters from request
        $filters = [
            'search' => $this->request->getGet('search'),
            'status' => $this->request->getGet('status'),
            'license_status' => $this->request->getGet('license_status'),
            'date_from' => $this->request->getGet('date_from'),
            'date_to' => $this->request->getGet('date_to'),
            'sort_field' => $this->request->getGet('sort_field', FILTER_SANITIZE_STRING) ?? 'created_at',
            'sort_direction' => $this->request->getGet('sort_direction', FILTER_SANITIZE_STRING) ?? 'DESC',
            'per_page' => $this->request->getGet('per_page', FILTER_SANITIZE_NUMBER_INT) ?? 25
        ];

        // Build query with filters
        $builder = $this->getOrganizationModel()->select('*');

        // Apply search filter
        if (!empty($filters['search'])) {
            $builder->groupStart()
                   ->like('name', $filters['search'])
                   ->orLike('org_code', $filters['search'])
                   ->orLike('description', $filters['search'])
                   ->orLike('contact_email', $filters['search'])
                   ->groupEnd();
        }

        // Apply status filters
        if (!empty($filters['status'])) {
            $builder->where('is_active', $filters['status'] === 'active' ? 1 : 0);
        }

        if (!empty($filters['license_status'])) {
            $builder->where('license_status', $filters['license_status']);
        }

        // Apply date range filter
        if (!empty($filters['date_from'])) {
            $builder->where('created_at >=', $filters['date_from'] . ' 00:00:00');
        }

        if (!empty($filters['date_to'])) {
            $builder->where('created_at <=', $filters['date_to'] . ' 23:59:59');
        }

        // Apply sorting
        $allowedSortFields = ['name', 'org_code', 'created_at', 'license_status', 'is_active'];
        if (in_array($filters['sort_field'], $allowedSortFields)) {
            $builder->orderBy($filters['sort_field'], $filters['sort_direction']);
        }

        // Get paginated results
        $organizations = $builder->paginate($filters['per_page']);
        $pager = $this->getOrganizationModel()->pager;

        // Get statistics for summary
        $stats = [
            'total' => $this->getOrganizationModel()->countAllResults(false),
            'active' => $this->getOrganizationModel()->where('is_active', 1)->countAllResults(false),
            'paid' => $this->getOrganizationModel()->where('license_status', 'paid')->countAllResults(false),
            'unpaid' => $this->getOrganizationModel()->where('license_status', 'unpaid')->countAllResults(false)
        ];

        $data = [
            'title' => 'Organizations - Dakoii Portal',
            'page_title' => 'Organizations',
            'organizations' => $organizations,
            'pager' => $pager,
            'filters' => $filters,
            'stats' => $stats,
            'user_name' => $this->session->get('dakoii_name')
        ];

        return view('dakoii/dakoii_organizations_list', $data);
    }

    /**
     * Show create organization form
     * Task 3.2: showCreateOrganisationForm
     */
    public function showCreateOrganisationForm()
    {
        $data = [
            'title' => 'Create Organization - Dakoii Portal',
            'page_title' => 'Create New Organization',
            'user_name' => $this->session->get('dakoii_name'),
            'validation' => \Config\Services::validation()
        ];

        return view('dakoii/dakoii_organizations_create', $data);
    }

    /**
     * Create new organization
     * Task 3.3: createOrganisation
     */
    public function createOrganisation()
    {
        $userId = $this->session->get('dakoii_user_id');

        // Validation rules
        $rules = [
            'name' => [
                'label' => 'Organization Name',
                'rules' => 'required|min_length[3]|max_length[150]|is_unique[organizations.name]',
                'errors' => [
                    'required' => 'Organization name is required.',
                    'min_length' => 'Organization name must be at least 3 characters long.',
                    'max_length' => 'Organization name cannot exceed 150 characters.',
                    'is_unique' => 'An organization with this name already exists.'
                ]
            ],
            'description' => [
                'label' => 'Description',
                'rules' => 'permit_empty|max_length[1000]'
            ],
            'contact_email' => [
                'label' => 'Contact Email',
                'rules' => 'permit_empty|valid_email|max_length[100]'
            ],
            'contact_phone' => [
                'label' => 'Contact Phone',
                'rules' => 'permit_empty|max_length[20]'
            ],
            'website_url' => [
                'label' => 'Website URL',
                'rules' => 'permit_empty|valid_url|max_length[150]'
            ],
            'address_line1' => [
                'label' => 'Address Line 1',
                'rules' => 'permit_empty|max_length[150]'
            ],
            'city' => [
                'label' => 'City',
                'rules' => 'permit_empty|max_length[100]'
            ],
            'country' => [
                'label' => 'Country',
                'rules' => 'permit_empty|max_length[100]'
            ]
        ];

        if (!$this->validate($rules)) {
            return redirect()->back()->withInput()->with('errors', $this->validator->getErrors());
        }

        // Generate unique organization code
        $orgCode = $this->generateOrganisationCode();

        // Prepare data for insertion
        $data = [
            'org_code' => $orgCode,
            'name' => $this->request->getPost('name'),
            'description' => $this->request->getPost('description'),
            'license_status' => 'paid', // Default as specified
            'is_active' => 1, // Default as specified
            'contact_email' => $this->request->getPost('contact_email'),
            'contact_phone' => $this->request->getPost('contact_phone'),
            'website_url' => $this->request->getPost('website_url'),
            'address_line1' => $this->request->getPost('address_line1'),
            'address_line2' => $this->request->getPost('address_line2'),
            'city' => $this->request->getPost('city'),
            'state' => $this->request->getPost('state'),
            'postal_code' => $this->request->getPost('postal_code'),
            'country' => $this->request->getPost('country'),
            'facebook_url' => $this->request->getPost('facebook_url'),
            'twitter_url' => $this->request->getPost('twitter_url'),
            'linkedin_url' => $this->request->getPost('linkedin_url'),
            'instagram_url' => $this->request->getPost('instagram_url'),
            'created_by' => $userId
        ];

        // Handle GPS coordinates if provided
        $lat = $this->request->getPost('hq_lat');
        $lng = $this->request->getPost('hq_lng');
        if (!empty($lat) && !empty($lng)) {
            $data['hq_lat'] = $lat;
            $data['hq_lng'] = $lng;
        }

        // Start database transaction
        $db = \Config\Database::connect();
        $db->transStart();

        try {
            // Insert organization
            $organizationId = $this->organizationModel->insert($data);

            if (!$organizationId) {
                throw new \Exception('Failed to create organization');
            }

            // Handle logo upload if provided
            $logoFile = $this->request->getFile('logo');
            if ($logoFile && $logoFile->isValid() && !$logoFile->hasMoved()) {
                $logoPath = $this->handleFileUpload($logoFile, 'logos', $organizationId);
                if ($logoPath) {
                    $this->organizationModel->update($organizationId, ['logo_path' => $logoPath]);
                }
            }

            // Handle wallpaper upload if provided
            $wallpaperFile = $this->request->getFile('wallpaper');
            if ($wallpaperFile && $wallpaperFile->isValid() && !$wallpaperFile->hasMoved()) {
                $wallpaperPath = $this->handleFileUpload($wallpaperFile, 'wallpapers', $organizationId);
                if ($wallpaperPath) {
                    $this->organizationModel->update($organizationId, ['wallpaper_path' => $wallpaperPath]);
                }
            }

            $db->transComplete();

            if ($db->transStatus() === false) {
                throw new \Exception('Transaction failed');
            }

            // Log successful creation
            log_message('info', 'Organization created: ' . $data['name'] . ' (ID: ' . $organizationId . ') by user: ' . $userId);

            return redirect()->to('/dakoii/organizations/' . $organizationId)
                           ->with('success', 'Organization "' . $data['name'] . '" has been created successfully with code: ' . $orgCode);

        } catch (\Exception $e) {
            $db->transRollback();
            log_message('error', 'Failed to create organization: ' . $e->getMessage());
            return redirect()->back()->withInput()->with('error', 'Failed to create organization. Please try again.');
        }
    }

    /**
     * Generate unique 5-digit organization code
     * Task 3.4: generateOrganisationCode
     */
    public function generateOrganisationCode(): string
    {
        $maxAttempts = 100;
        $attempt = 0;

        do {
            // Generate 5-digit code
            $code = str_pad(rand(10000, 99999), 5, '0', STR_PAD_LEFT);
            $attempt++;
            
            // Check if code exists
            $exists = $this->organizationModel->where('org_code', $code)->countAllResults() > 0;
            
        } while ($exists && $attempt < $maxAttempts);

        if ($attempt >= $maxAttempts) {
            throw new \RuntimeException('Unable to generate unique organization code after ' . $maxAttempts . ' attempts');
        }

        return $code;
    }

    /**
     * Handle file upload
     */
    private function handleFileUpload($file, $type, $organizationId): ?string
    {
        if (!$file->isValid()) {
            return null;
        }

        // Validate file type
        $allowedTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'];
        if (!in_array($file->getMimeType(), $allowedTypes)) {
            return null;
        }

        // Validate file size (max 5MB)
        if ($file->getSize() > 5 * 1024 * 1024) {
            return null;
        }

        // Create directory if it doesn't exist
        $uploadPath = WRITEPATH . 'uploads/organizations/' . $organizationId . '/' . $type . '/';
        if (!is_dir($uploadPath)) {
            mkdir($uploadPath, 0755, true);
        }

        // Generate unique filename
        $extension = $file->getClientExtension();
        $filename = uniqid() . '.' . $extension;
        $fullPath = $uploadPath . $filename;

        // Move file
        if ($file->move($uploadPath, $filename)) {
            return 'uploads/organizations/' . $organizationId . '/' . $type . '/' . $filename;
        }

        return null;
    }

    /**
     * View organization profile
     * Task 3.5: viewOrganisationProfile
     */
    public function viewOrganisationProfile($id)
    {
        $organization = $this->organizationModel->find($id);

        if (!$organization) {
            return redirect()->to('/dakoii/organizations')->with('error', 'Organization not found.');
        }

        // Get organization images
        $images = $this->organizationImageModel
            ->where('organization_id', $id)
            ->orderBy('sort_order', 'ASC')
            ->findAll();

        // Get organization statistics (this would include admin count when admin table exists)
        $stats = [
            'created_date' => $organization['created_at'],
            'last_updated' => $organization['updated_at'],
            'status' => $organization['is_active'] ? 'Active' : 'Inactive',
            'license_status' => ucfirst($organization['license_status'])
        ];

        $data = [
            'title' => $organization['name'] . ' - Organization Profile',
            'page_title' => $organization['name'],
            'organization' => $organization,
            'images' => $images,
            'stats' => $stats,
            'user_name' => $this->session->get('dakoii_name')
        ];

        return view('organizations/profile', $data);
    }

    /**
     * Toggle organization status
     * Task 3.9: toggleOrganisationStatus
     */
    public function toggleOrganisationStatus($id)
    {
        $userId = $this->session->get('dakoii_user_id');
        $organization = $this->organizationModel->find($id);

        if (!$organization) {
            return $this->response->setJSON(['success' => false, 'message' => 'Organization not found']);
        }

        $newStatus = $organization['is_active'] ? 0 : 1;
        $statusText = $newStatus ? 'activated' : 'deactivated';

        $updated = $this->organizationModel->update($id, [
            'is_active' => $newStatus,
            'updated_by' => $userId
        ]);

        if ($updated) {
            log_message('info', 'Organization status changed: ' . $organization['name'] . ' (ID: ' . $id . ') ' . $statusText . ' by user: ' . $userId);
            
            return $this->response->setJSON([
                'success' => true, 
                'message' => 'Organization has been ' . $statusText . ' successfully.',
                'new_status' => $newStatus
            ]);
        }

        return $this->response->setJSON(['success' => false, 'message' => 'Failed to update organization status']);
    }

    /**
     * Change organization license status
     * Task 3.10: changeOrganisationLicenseStatus
     */
    public function changeOrganisationLicenseStatus($id)
    {
        $userId = $this->session->get('dakoii_user_id');
        $organization = $this->organizationModel->find($id);

        if (!$organization) {
            return $this->response->setJSON(['success' => false, 'message' => 'Organization not found']);
        }

        $newLicenseStatus = $organization['license_status'] === 'paid' ? 'unpaid' : 'paid';

        $updated = $this->organizationModel->update($id, [
            'license_status' => $newLicenseStatus,
            'updated_by' => $userId
        ]);

        if ($updated) {
            log_message('info', 'Organization license status changed: ' . $organization['name'] . ' (ID: ' . $id . ') to ' . $newLicenseStatus . ' by user: ' . $userId);
            
            return $this->response->setJSON([
                'success' => true, 
                'message' => 'License status has been changed to ' . $newLicenseStatus . ' successfully.',
                'new_license_status' => $newLicenseStatus
            ]);
        }

        return $this->response->setJSON(['success' => false, 'message' => 'Failed to update license status']);
    }

    /**
     * Soft delete organization
     * Task 3.11: softDeleteOrganisation
     */
    public function softDeleteOrganisation($id)
    {
        $userId = $this->session->get('dakoii_user_id');
        $organization = $this->organizationModel->find($id);

        if (!$organization) {
            return $this->response->setJSON(['success' => false, 'message' => 'Organization not found']);
        }

        // Update the deleted_by field before soft delete
        $this->organizationModel->update($id, ['deleted_by' => $userId]);

        // Perform soft delete
        $deleted = $this->organizationModel->delete($id);

        if ($deleted) {
            log_message('info', 'Organization soft deleted: ' . $organization['name'] . ' (ID: ' . $id . ') by user: ' . $userId);

            return $this->response->setJSON([
                'success' => true,
                'message' => 'Organization "' . $organization['name'] . '" has been deleted successfully.'
            ]);
        }

        return $this->response->setJSON(['success' => false, 'message' => 'Failed to delete organization']);
    }

    /**
     * Export organizations data
     */
    public function exportOrganizations()
    {
        $format = $this->request->getGet('format', FILTER_SANITIZE_STRING) ?? 'csv';

        // Get all organizations (respecting current filters if any)
        $organizations = $this->organizationModel->findAll();

        if ($format === 'json') {
            return $this->response
                ->setHeader('Content-Type', 'application/json')
                ->setHeader('Content-Disposition', 'attachment; filename="organizations_' . date('Y-m-d') . '.json"')
                ->setJSON($organizations);
        }

        // Default to CSV
        $csv = "ID,Code,Name,License Status,Status,Contact Email,Created Date\n";
        foreach ($organizations as $org) {
            $csv .= sprintf(
                "%d,%s,%s,%s,%s,%s,%s\n",
                $org['id'],
                $org['org_code'],
                '"' . str_replace('"', '""', $org['name']) . '"',
                $org['license_status'],
                $org['is_active'] ? 'Active' : 'Inactive',
                $org['contact_email'] ?? '',
                $org['created_at']
            );
        }

        return $this->response
            ->setHeader('Content-Type', 'text/csv')
            ->setHeader('Content-Disposition', 'attachment; filename="organizations_' . date('Y-m-d') . '.csv"')
            ->setBody($csv);
    }

    // Organization Admin Management Methods (using existing DakoiiUser model)

    /**
     * List organization admins (using DakoiiUser model)
     * Task 4.1: listOrgAdmins
     */
    public function listOrgAdmins($organizationId)
    {
        $organization = $this->organizationModel->find($organizationId);

        if (!$organization) {
            return redirect()->to('/dakoii/organizations')->with('error', 'Organization not found.');
        }

        // For now, we'll show all Dakoii users as potential admins
        // In a real implementation, you'd have a relationship table
        $admins = $this->dakoiiUserModel->findAll();

        $data = [
            'title' => 'Administrators - ' . $organization['name'],
            'page_title' => 'Organization Administrators',
            'organization' => $organization,
            'admins' => $admins,
            'user_name' => $this->session->get('dakoii_name')
        ];

        return view('dakoii/dakoii_admins_list', $data);
    }

    /**
     * Show create admin form
     * Task 4.2: showCreateAdminForm
     */
    public function showCreateAdminForm($organizationId)
    {
        $organization = $this->organizationModel->find($organizationId);

        if (!$organization) {
            return redirect()->to('/dakoii/organizations')->with('error', 'Organization not found.');
        }

        $data = [
            'title' => 'Create Administrator - ' . $organization['name'],
            'page_title' => 'Create New Administrator',
            'organization' => $organization,
            'user_name' => $this->session->get('dakoii_name'),
            'validation' => \Config\Services::validation()
        ];

        return view('dakoii/dakoii_admins_create', $data);
    }

    /**
     * Create organization admin (using DakoiiUser model)
     * Task 4.3: createOrgAdmin
     */
    public function createOrgAdmin($organizationId)
    {
        $userId = $this->session->get('dakoii_user_id');
        $organization = $this->organizationModel->find($organizationId);

        if (!$organization) {
            return redirect()->to('/dakoii/organizations')->with('error', 'Organization not found.');
        }

        // Validation rules for admin creation
        $rules = [
            'username' => [
                'label' => 'Username',
                'rules' => 'required|min_length[3]|max_length[50]|is_unique[dakoii_users.username]',
                'errors' => [
                    'required' => 'Username is required.',
                    'min_length' => 'Username must be at least 3 characters long.',
                    'is_unique' => 'This username already exists.'
                ]
            ],
            'email' => [
                'label' => 'Email',
                'rules' => 'required|valid_email|is_unique[dakoii_users.email]',
                'errors' => [
                    'required' => 'Email is required.',
                    'valid_email' => 'Please enter a valid email address.',
                    'is_unique' => 'This email address already exists.'
                ]
            ],
            'name' => [
                'label' => 'Full Name',
                'rules' => 'required|min_length[2]|max_length[100]'
            ],
            'role' => [
                'label' => 'Role',
                'rules' => 'required|in_list[admin,moderator,user]'
            ]
        ];

        if (!$this->validate($rules)) {
            return redirect()->back()->withInput()->with('errors', $this->validator->getErrors());
        }

        // Generate admin code and temporary password
        $adminCode = $this->generateAdminCode();
        $tempPassword = $this->generateTemporaryPassword();
        $activationToken = bin2hex(random_bytes(32));

        $adminData = [
            'user_code' => $adminCode,
            'username' => $this->request->getPost('username'),
            'email' => $this->request->getPost('email'),
            'name' => $this->request->getPost('name'),
            'role' => $this->request->getPost('role'),
            'password_hash' => password_hash($tempPassword, PASSWORD_ARGON2ID),
            'activation_token' => $activationToken,
            'is_activated' => 0, // Requires activation
            'created_by' => $userId
        ];

        $adminId = $this->dakoiiUserModel->insert($adminData);

        if ($adminId) {
            // Send activation email (implement email service)
            // $this->sendAdminActivationEmail($adminData, $activationToken, $organizationId);

            log_message('info', 'Organization admin created: ' . $adminData['username'] . ' for organization: ' . $organization['name']);

            return redirect()->to('/dakoii/organizations/' . $organizationId . '/admins')
                           ->with('success', 'Administrator created successfully. Activation email sent to ' . $adminData['email']);
        }

        return redirect()->back()->withInput()->with('error', 'Failed to create administrator. Please try again.');
    }

    /**
     * Generate unique admin code
     * Task 4.4: generateAdminCode
     */
    private function generateAdminCode(): string
    {
        $maxAttempts = 100;
        $attempt = 0;

        do {
            // Generate 8-10 character alphanumeric code
            $length = rand(8, 10);
            $characters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
            $code = '';

            for ($i = 0; $i < $length; $i++) {
                $code .= $characters[rand(0, strlen($characters) - 1)];
            }

            $attempt++;
            $exists = $this->dakoiiUserModel->where('user_code', $code)->countAllResults() > 0;

        } while ($exists && $attempt < $maxAttempts);

        if ($attempt >= $maxAttempts) {
            throw new \RuntimeException('Unable to generate unique admin code after ' . $maxAttempts . ' attempts');
        }

        return $code;
    }

    /**
     * Generate temporary password
     */
    private function generateTemporaryPassword(): string
    {
        return str_pad(rand(1000, 9999), 4, '0', STR_PAD_LEFT);
    }

    /**
     * View organization admin profile
     * Task 4.5: viewOrgAdminProfile
     */
    public function viewOrgAdminProfile($adminId)
    {
        $admin = $this->dakoiiUserModel->find($adminId);

        if (!$admin) {
            return redirect()->to('/dakoii/organizations')->with('error', 'Administrator not found.');
        }

        // Get admin statistics and activity
        $stats = [
            'created_date' => $admin['created_at'],
            'last_login' => $admin['last_login_at'],
            'activation_status' => $admin['is_activated'] ? 'Activated' : 'Pending',
            'role' => ucfirst($admin['role'])
        ];

        $data = [
            'title' => $admin['name'] . ' - Administrator Profile',
            'page_title' => $admin['name'],
            'admin' => $admin,
            'stats' => $stats,
            'user_name' => $this->session->get('dakoii_name')
        ];

        return view('organizations/admins/profile', $data);
    }

    /**
     * Update organization admin
     * Task 4.7: updateOrgAdmin
     */
    public function updateOrgAdmin($adminId)
    {
        $userId = $this->session->get('dakoii_user_id');
        $admin = $this->dakoiiUserModel->find($adminId);

        if (!$admin) {
            return $this->response->setJSON(['success' => false, 'message' => 'Administrator not found']);
        }

        // Validation rules for admin update
        $rules = [
            'name' => 'required|min_length[2]|max_length[100]',
            'email' => 'required|valid_email',
            'role' => 'required|in_list[admin,moderator,user]'
        ];

        if (!$this->validate($rules)) {
            return $this->response->setJSON(['success' => false, 'errors' => $this->validator->getErrors()]);
        }

        $updateData = [
            'name' => $this->request->getPost('name'),
            'email' => $this->request->getPost('email'),
            'role' => $this->request->getPost('role'),
            'updated_by' => $userId
        ];

        $updated = $this->dakoiiUserModel->update($adminId, $updateData);

        if ($updated) {
            log_message('info', 'Organization admin updated: ' . $admin['username'] . ' (ID: ' . $adminId . ') by user: ' . $userId);

            return $this->response->setJSON([
                'success' => true,
                'message' => 'Administrator updated successfully.'
            ]);
        }

        return $this->response->setJSON(['success' => false, 'message' => 'Failed to update administrator']);
    }

    /**
     * Toggle admin status
     * Task 4.11: toggleAdminStatus
     */
    public function toggleAdminStatus($adminId)
    {
        $userId = $this->session->get('dakoii_user_id');
        $admin = $this->dakoiiUserModel->find($adminId);

        if (!$admin) {
            return $this->response->setJSON(['success' => false, 'message' => 'Administrator not found']);
        }

        $newStatus = $admin['is_activated'] ? 0 : 1;
        $statusText = $newStatus ? 'activated' : 'deactivated';

        $updated = $this->dakoiiUserModel->update($adminId, [
            'is_activated' => $newStatus,
            'updated_by' => $userId
        ]);

        if ($updated) {
            log_message('info', 'Admin status changed: ' . $admin['username'] . ' (ID: ' . $adminId . ') ' . $statusText . ' by user: ' . $userId);

            return $this->response->setJSON([
                'success' => true,
                'message' => 'Administrator has been ' . $statusText . ' successfully.',
                'new_status' => $newStatus
            ]);
        }

        return $this->response->setJSON(['success' => false, 'message' => 'Failed to update administrator status']);
    }

    /**
     * Reset admin password
     * Task 4.10: resetAdminPassword
     */
    public function resetAdminPassword($adminId)
    {
        $userId = $this->session->get('dakoii_user_id');
        $admin = $this->dakoiiUserModel->find($adminId);

        if (!$admin) {
            return $this->response->setJSON(['success' => false, 'message' => 'Administrator not found']);
        }

        // Generate new temporary password
        $tempPassword = $this->generateTemporaryPassword();
        $hashedPassword = password_hash($tempPassword, PASSWORD_ARGON2ID);

        $updated = $this->dakoiiUserModel->update($adminId, [
            'password_hash' => $hashedPassword,
            'updated_by' => $userId
        ]);

        if ($updated) {
            // Send password reset email (implement email service)
            // $this->sendPasswordResetEmail($admin, $tempPassword);

            log_message('info', 'Admin password reset: ' . $admin['username'] . ' (ID: ' . $adminId . ') by user: ' . $userId);

            return $this->response->setJSON([
                'success' => true,
                'message' => 'Password reset successfully. New temporary password: ' . $tempPassword
            ]);
        }

        return $this->response->setJSON(['success' => false, 'message' => 'Failed to reset password']);
    }

    /**
     * Soft delete organization admin
     * Task 4.12: softDeleteOrgAdmin
     */
    public function softDeleteOrgAdmin($adminId)
    {
        $userId = $this->session->get('dakoii_user_id');
        $admin = $this->dakoiiUserModel->find($adminId);

        if (!$admin) {
            return $this->response->setJSON(['success' => false, 'message' => 'Administrator not found']);
        }

        // Update the deleted_by field before soft delete
        $this->dakoiiUserModel->update($adminId, ['deleted_by' => $userId]);

        // Perform soft delete
        $deleted = $this->dakoiiUserModel->delete($adminId);

        if ($deleted) {
            log_message('info', 'Organization admin soft deleted: ' . $admin['username'] . ' (ID: ' . $adminId . ') by user: ' . $userId);

            return $this->response->setJSON([
                'success' => true,
                'message' => 'Administrator "' . $admin['name'] . '" has been deleted successfully.'
            ]);
        }

        return $this->response->setJSON(['success' => false, 'message' => 'Failed to delete administrator']);
    }
}
