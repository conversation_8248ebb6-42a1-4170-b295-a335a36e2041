<?= $this->extend('templates/dakoii_portal_template') ?>

<?= $this->section('sidebar') ?>
<div class="nav-item">
    <a href="<?= base_url('dakoii/dashboard') ?>" class="nav-link">
        <span class="nav-icon">📊</span>
        <span class="nav-text">Dashboard</span>
    </a>
</div>
<div class="nav-item">
    <a href="<?= base_url('dakoii/organizations') ?>" class="nav-link active">
        <span class="nav-icon">🏢</span>
        <span class="nav-text">Organizations</span>
    </a>
</div>
<div class="nav-item">
    <a href="<?= base_url('dakoii/users') ?>" class="nav-link">
        <span class="nav-icon">👥</span>
        <span class="nav-text">Users</span>
    </a>
</div>
<div class="nav-item" style="margin-top: auto;">
    <a href="<?= base_url('dakoii/logout') ?>" class="nav-link">
        <span class="nav-icon">🚪</span>
        <span class="nav-text">Logout</span>
    </a>
</div>
<?= $this->endSection() ?>

<?= $this->section('header_actions') ?>
<a href="<?= base_url('dakoii/organizations') ?>" class="btn btn-secondary">
    ← Back to Organizations
</a>
<button onclick="editAdmin(<?= $admin['id'] ?>)" class="btn btn-primary">
    Edit Administrator
</button>
<?= $this->endSection() ?>

<?= $this->section('content') ?>
<div class="fade-in">
    <!-- Admin Header -->
    <div class="card" style="margin-bottom: var(--spacing-xl);">
        <div style="display: flex; align-items: center; gap: var(--spacing-xl);">
            <?php if ($admin['id_photo_path']): ?>
                <img src="<?= base_url($admin['id_photo_path']) ?>" alt="Photo" style="width: 96px; height: 96px; border-radius: 50%; object-fit: cover;">
            <?php else: ?>
                <div style="width: 96px; height: 96px; border-radius: 50%; background: var(--gradient-secondary); display: flex; align-items: center; justify-content: center; color: white; font-weight: 700; font-size: 2rem;">
                    <?= strtoupper(substr($admin['name'], 0, 1)) ?>
                </div>
            <?php endif; ?>
            <div style="flex: 1;">
                <h1 style="margin: 0; color: var(--text-primary); font-size: 2rem;"><?= esc($admin['name']) ?></h1>
                <div style="color: var(--text-secondary); margin-top: var(--spacing-sm); font-size: 1.125rem;">
                    <?= esc($admin['email']) ?>
                </div>
                <div style="margin-top: var(--spacing-md); display: flex; gap: var(--spacing-md); align-items: center;">
                    <span class="status-badge status-<?= $admin['is_activated'] ? 'active' : 'pending' ?>">
                        <?= $admin['is_activated'] ? 'Active' : 'Pending' ?>
                    </span>
                    <span class="role-badge role-<?= $admin['role'] ?>">
                        <?= ucfirst($admin['role']) ?>
                    </span>
                </div>
            </div>
            <div style="text-align: right;">
                <div style="font-size: 0.875rem; color: var(--text-tertiary); margin-bottom: var(--spacing-xs);">Admin Code</div>
                <div style="font-family: var(--font-mono); font-weight: 700; color: var(--text-primary); font-size: 1.25rem;">
                    <?= esc($admin['user_code']) ?>
                </div>
            </div>
        </div>
    </div>

    <!-- Admin Details -->
    <div style="display: grid; grid-template-columns: 2fr 1fr; gap: var(--spacing-xl);">
        <!-- Main Information -->
        <div>
            <!-- Account Information -->
            <div class="card" style="margin-bottom: var(--spacing-xl);">
                <div class="card-header">Account Information</div>
                
                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: var(--spacing-lg);">
                    <div>
                        <div style="font-size: 0.875rem; color: var(--text-tertiary); margin-bottom: var(--spacing-xs);">Username</div>
                        <div style="color: var(--text-primary); font-weight: 600;"><?= esc($admin['username']) ?></div>
                    </div>
                    <div>
                        <div style="font-size: 0.875rem; color: var(--text-tertiary); margin-bottom: var(--spacing-xs);">Role</div>
                        <div style="color: var(--text-primary); font-weight: 600;"><?= ucfirst($admin['role']) ?></div>
                    </div>
                    <div>
                        <div style="font-size: 0.875rem; color: var(--text-tertiary); margin-bottom: var(--spacing-xs);">Status</div>
                        <div style="color: var(--text-primary); font-weight: 600;"><?= $stats['activation_status'] ?></div>
                    </div>
                    <div>
                        <div style="font-size: 0.875rem; color: var(--text-tertiary); margin-bottom: var(--spacing-xs);">Admin Code</div>
                        <div style="color: var(--text-primary); font-weight: 600; font-family: var(--font-mono);"><?= esc($admin['user_code']) ?></div>
                    </div>
                </div>
            </div>

            <!-- Activity Information -->
            <div class="card" style="margin-bottom: var(--spacing-xl);">
                <div class="card-header">Activity Information</div>
                
                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: var(--spacing-lg);">
                    <div>
                        <div style="font-size: 0.875rem; color: var(--text-tertiary); margin-bottom: var(--spacing-xs);">Account Created</div>
                        <div style="color: var(--text-primary); font-weight: 600;">
                            <?= date('M j, Y \a\t g:i A', strtotime($stats['created_date'])) ?>
                        </div>
                    </div>
                    <div>
                        <div style="font-size: 0.875rem; color: var(--text-tertiary); margin-bottom: var(--spacing-xs);">Last Login</div>
                        <div style="color: var(--text-primary); font-weight: 600;">
                            <?= $stats['last_login'] ? date('M j, Y \a\t g:i A', strtotime($stats['last_login'])) : 'Never' ?>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Security Information -->
            <div class="card">
                <div class="card-header">Security Information</div>
                
                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: var(--spacing-lg);">
                    <div>
                        <div style="font-size: 0.875rem; color: var(--text-tertiary); margin-bottom: var(--spacing-xs);">Activation Token</div>
                        <div style="color: var(--text-primary); font-weight: 600; font-family: var(--font-mono); font-size: 0.875rem;">
                            <?= $admin['activation_token'] ? substr($admin['activation_token'], 0, 8) . '...' : 'None' ?>
                        </div>
                    </div>
                    <div>
                        <div style="font-size: 0.875rem; color: var(--text-tertiary); margin-bottom: var(--spacing-xs);">Password Reset Token</div>
                        <div style="color: var(--text-primary); font-weight: 600; font-family: var(--font-mono); font-size: 0.875rem;">
                            <?= $admin['password_reset_token'] ? substr($admin['password_reset_token'], 0, 8) . '...' : 'None' ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Actions Sidebar -->
        <div>
            <!-- Quick Actions -->
            <div class="card" style="margin-bottom: var(--spacing-xl);">
                <div class="card-header">Quick Actions</div>
                
                <div style="display: flex; flex-direction: column; gap: var(--spacing-md);">
                    <button onclick="editAdmin(<?= $admin['id'] ?>)" class="btn btn-primary" style="width: 100%;">
                        ✏️ Edit Administrator
                    </button>
                    
                    <button onclick="toggleAdminStatus(<?= $admin['id'] ?>, '<?= $admin['is_activated'] ? 'deactivate' : 'activate' ?>')" class="btn btn-secondary" style="width: 100%;">
                        <?= $admin['is_activated'] ? '⏸️ Deactivate' : '▶️ Activate' ?>
                    </button>
                    
                    <button onclick="resetPassword(<?= $admin['id'] ?>)" class="btn btn-secondary" style="width: 100%;">
                        🔑 Reset Password
                    </button>
                    
                    <?php if (!$admin['is_activated']): ?>
                        <button onclick="resendActivation(<?= $admin['id'] ?>)" class="btn btn-secondary" style="width: 100%;">
                            📧 Resend Activation
                        </button>
                    <?php endif; ?>
                    
                    <hr style="border: none; border-top: 1px solid var(--glass-border); margin: var(--spacing-md) 0;">
                    
                    <button onclick="deleteAdmin(<?= $admin['id'] ?>, '<?= esc($admin['name']) ?>')" class="btn" style="width: 100%; background: rgba(255, 0, 110, 0.1); color: #FF006E; border: 1px solid rgba(255, 0, 110, 0.3);">
                        🗑️ Delete Administrator
                    </button>
                </div>
            </div>

            <!-- Statistics -->
            <div class="card">
                <div class="card-header">Statistics</div>
                
                <div style="display: flex; flex-direction: column; gap: var(--spacing-md);">
                    <div style="display: flex; justify-content: space-between; align-items: center;">
                        <span style="color: var(--text-secondary);">Account Age</span>
                        <span style="color: var(--text-primary); font-weight: 600;">
                            <?= floor((time() - strtotime($admin['created_at'])) / (60 * 60 * 24)) ?> days
                        </span>
                    </div>
                    
                    <div style="display: flex; justify-content: space-between; align-items: center;">
                        <span style="color: var(--text-secondary);">Login Count</span>
                        <span style="color: var(--text-primary); font-weight: 600;">-</span>
                    </div>
                    
                    <div style="display: flex; justify-content: space-between; align-items: center;">
                        <span style="color: var(--text-secondary);">Last Activity</span>
                        <span style="color: var(--text-primary); font-weight: 600;">
                            <?= $admin['last_login_at'] ? floor((time() - strtotime($admin['last_login_at'])) / (60 * 60 * 24)) . ' days ago' : 'Never' ?>
                        </span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.status-badge {
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--radius-sm);
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.status-active {
    background: rgba(6, 255, 165, 0.1);
    color: #06FFA5;
}

.status-pending {
    background: rgba(255, 183, 0, 0.1);
    color: #FFB700;
}

.role-badge {
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--radius-sm);
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.role-admin {
    background: rgba(255, 0, 110, 0.1);
    color: #FF006E;
}

.role-moderator {
    background: rgba(131, 56, 236, 0.1);
    color: #8338EC;
}

.role-user {
    background: rgba(0, 212, 255, 0.1);
    color: #00D4FF;
}
</style>

<script>
function editAdmin(adminId) {
    // Implement edit functionality
    alert('Edit functionality would be implemented here');
}

function toggleAdminStatus(adminId, action) {
    if (confirm('Are you sure you want to ' + action + ' this administrator?')) {
        fetch('<?= base_url('dakoii/organizations/admins/') ?>' + adminId + '/toggle-status', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-Requested-With': 'XMLHttpRequest'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('Error: ' + data.message);
            }
        })
        .catch(error => {
            alert('An error occurred. Please try again.');
        });
    }
}

function resetPassword(adminId) {
    if (confirm('Are you sure you want to reset the password for this administrator? A new temporary password will be generated.')) {
        fetch('<?= base_url('dakoii/organizations/admins/') ?>' + adminId + '/reset-password', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-Requested-With': 'XMLHttpRequest'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert(data.message);
            } else {
                alert('Error: ' + data.message);
            }
        })
        .catch(error => {
            alert('An error occurred. Please try again.');
        });
    }
}

function resendActivation(adminId) {
    if (confirm('Are you sure you want to resend the activation email?')) {
        // Implement resend activation functionality
        alert('Resend activation functionality would be implemented here');
    }
}

function deleteAdmin(adminId, name) {
    if (confirm('Are you sure you want to delete administrator "' + name + '"? This action cannot be undone.')) {
        fetch('<?= base_url('dakoii/organizations/admins/') ?>' + adminId, {
            method: 'DELETE',
            headers: {
                'Content-Type': 'application/json',
                'X-Requested-With': 'XMLHttpRequest'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                window.location.href = '<?= base_url('dakoii/organizations') ?>';
            } else {
                alert('Error: ' + data.message);
            }
        })
        .catch(error => {
            alert('An error occurred. Please try again.');
        });
    }
}
</script>
<?= $this->endSection() ?>
