<?php

namespace App\Database\Seeds;

use CodeIgniter\Database\Seeder;

class OrganizationSeeder extends Seeder
{
    public function run()
    {
        $data = [
            [
                'org_code' => '12345',
                'name' => 'Sample Organization',
                'description' => 'This is a sample organization for testing purposes.',
                'license_status' => 'paid',
                'is_active' => 1,
                'contact_email' => '<EMAIL>',
                'contact_phone' => '******-123-4567',
                'address_line1' => '123 Main Street',
                'city' => 'Sample City',
                'state' => 'Sample State',
                'postal_code' => '12345',
                'country' => 'Sample Country',
                'website_url' => 'https://www.sample.org',
                'created_at' => date('Y-m-d H:i:s'),
                'created_by' => 1,
            ],
            [
                'org_code' => '67890',
                'name' => 'Test Organization',
                'description' => 'Another test organization with unpaid license.',
                'license_status' => 'unpaid',
                'is_active' => 1,
                'contact_email' => '<EMAIL>',
                'contact_phone' => '******-987-6543',
                'address_line1' => '456 Test Avenue',
                'city' => 'Test City',
                'state' => 'Test State',
                'postal_code' => '67890',
                'country' => 'Test Country',
                'website_url' => 'https://www.test.org',
                'created_at' => date('Y-m-d H:i:s'),
                'created_by' => 1,
            ],
            [
                'org_code' => '11111',
                'name' => 'Inactive Organization',
                'description' => 'This organization is inactive for testing.',
                'license_status' => 'paid',
                'is_active' => 0,
                'contact_email' => '<EMAIL>',
                'created_at' => date('Y-m-d H:i:s'),
                'created_by' => 1,
            ]
        ];

        $builder = $this->db->table('organizations');
        
        foreach ($data as $org) {
            // Check if organization already exists
            $existing = $builder->where('org_code', $org['org_code'])->get()->getRow();
            
            if (!$existing) {
                $builder->insert($org);
                echo "Created organization: " . $org['name'] . " (" . $org['org_code'] . ")\n";
            } else {
                echo "Organization already exists: " . $org['name'] . " (" . $org['org_code'] . ")\n";
            }
        }
    }
}
